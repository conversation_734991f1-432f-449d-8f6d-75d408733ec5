<?php
header('Content-Type: application/json');
require_once __DIR__ . '/../commen/db_connection.php';

// السماح فقط بطلبات POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405); // Method Not Allowed
    echo json_encode(["error" => "Only POST method is allowed."]); 
    exit;
}

// قراءة البيانات من JSON
$input = json_decode(file_get_contents('php://input'), true);
$email = isset($input['email']) ? trim($input['email']) : null;
$password = isset($input['password']) ? $input['password'] : null;

// التحقق من توفر البريد وكلمة المرور
if (empty($email) || empty($password)) {
    http_response_code(400); // Bad Request
    echo json_encode(["error" => "Email and password are required."]);
    exit;
}

try {
    // جلب بيانات المستخدم حسب البريد الإلكتروني
    $sql = "SELECT idUtilisateur, email, motDePasse, role, etatCompte FROM Utilisateur WHERE email = :email";
    $stmt = $pdo->prepare($sql);
    $stmt->execute(['email' => $email]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    // التحقق من كلمة المرور المشفرة
    if ($user && password_verify($password, $user['motDePasse'])) {
        unset($user['motDePasse']); // إزالة كلمة السر من البيانات المعادة
        echo json_encode($user, JSON_UNESCAPED_UNICODE);
    } else {
        http_response_code(401); // Unauthorized
        echo json_encode(["error" => "Invalid email or password."]);
    }
} catch (PDOException $e) {
    http_response_code(500); // Internal Server Error
    echo json_encode(["error" => "Database error: " . $e->getMessage()]);
    exit;
}
