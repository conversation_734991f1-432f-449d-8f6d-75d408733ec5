<?php
// تحديد نوع الاستجابة كـ JSON
header('Content-Type: application/json; charset=UTF-8');

// تضمين الاتصال بقاعدة البيانات
require_once __DIR__ . '/../commen/db_connection.php';

// قراءة البيانات من الطلب بصيغة JSON
$input = json_decode(file_get_contents('php://input'), true);

// التحقق من أن الحقول الثلاثة موجودة
if (!isset($input['type'], $input['capacite'], $input['matricule'])) {
    echo json_encode(['error' => 'Tous les champs sont requis (type, capacite, matricule).']);
    exit;
}

// استخراج البيانات
$type = $input['type'];
$capacite = intval($input['capacite']);
$matricule = $input['matricule'];

try {
    // تجهيز الاستعلام لإدخال وسيلة النقل
    $stmt = $pdo->prepare("INSERT INTO MoyenDeTransport (type, capacite, matricule) VALUES (:type, :capacite, :matricule)");
    $stmt->bindParam(':type', $type);
    $stmt->bindParam(':capacite', $capacite);
    $stmt->bindParam(':matricule', $matricule);
    $stmt->execute();

    // الحصول على معرف وسيلة النقل التي تم إضافتها
    $idMoyen = $pdo->lastInsertId();

    // إنشاء الكائن النهائي للرد
    $moyen = [
        'idMoyen' => $idMoyen,
        'type' => $type,
        'capacite' => $capacite,
        'matricule' => $matricule
    ];

    // إرسال الرد على شكل JSON
    echo json_encode($moyen);
} catch (PDOException $e) {
    // في حالة حدوث خطأ في قاعدة البيانات
    echo json_encode(['error' => 'Erreur base de données : ' . $e->getMessage()]);
}
