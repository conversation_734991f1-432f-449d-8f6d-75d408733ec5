<?php
header('Content-Type: application/json; charset=UTF-8');
require_once __DIR__ . '/../commen/db_connection.php';

try {
    // تنفيذ الاستعلام لاستخراج جميع الرحلات
    $sql = "SELECT idTrajet, lieuDepart, lieuArrivee FROM Trajet";
    $stmt = $pdo->query($sql);
    $trajets = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // إرجاع النتائج بصيغة JSON
    echo json_encode($trajets, JSON_UNESCAPED_UNICODE);
} catch (PDOException $e) {
    // إرجاع رسالة خطأ في حالة وجود خطأ في قاعدة البيانات
    http_response_code(500);
    echo json_encode(["error" => "Erreur base de données : " . $e->getMessage()]);
}
