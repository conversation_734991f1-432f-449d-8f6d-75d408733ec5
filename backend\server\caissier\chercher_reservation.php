<?php
header('Content-Type: application/json; charset=UTF-8');
require_once __DIR__ . '/../commen/db_connection.php';

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(["error" => "Méthode non autorisée"]);
    exit;
}

$nom = isset($_GET['nom']) ? trim($_GET['nom']) : '';
$prenom = isset($_GET['prenom']) ? trim($_GET['prenom']) : '';

if (empty($nom) && empty($prenom)) {
    http_response_code(400);
    echo json_encode(["error" => "Veuillez fournir au moins le nom ou le prénom pour la recherche"]);
    exit;
}

try {
    $sql = "
        SELECT 
            r.idReservation,
            r.dateReservation,
            r.etat,
            r.archive,
            p.nom AS nomPassager,
            p.prenom AS prenomPassager,
            t.lieuDepart,
            t.lieuArrivee,
            s.heureDepart,
            s.heureArrivee,
            s.prix,
            m.type AS typeMoyen
        FROM Réservation r
        LEFT JOIN Passager p ON r.idPassager = p.idPassager
        LEFT JOIN ServiceDeTransport s ON r.idService = s.idService
        LEFT JOIN Trajet t ON s.idTrajet = t.idTrajet
        LEFT JOIN MoyenDeTransport m ON s.idMoyen = m.idMoyen
        WHERE 1=1
    ";

    $params = [];

    if (!empty($nom)) {
        $sql .= " AND p.nom LIKE :nom";
        $params[':nom'] = '%' . $nom . '%';
    }

    if (!empty($prenom)) {
        $sql .= " AND p.prenom LIKE :prenom";
        $params[':prenom'] = '%' . $prenom . '%';
    }

    $sql .= " ORDER BY r.dateReservation DESC";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode($results, JSON_UNESCAPED_UNICODE);

} catch (PDOException $e) {
    echo json_encode(["error" => "Erreur base de données : " . $e->getMessage()]);
}
