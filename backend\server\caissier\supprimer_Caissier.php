<?php
// تحديد نوع المحتوى للإخراج JSON مع ترميز UTF-8
header('Content-Type: application/json; charset=UTF-8');

// استدعاء ملف الاتصال بقاعدة البيانات
require_once __DIR__ . '/../commen/db_connection.php';

// قراءة البيانات القادمة من POST body بصيغة JSON
$json = file_get_contents('php://input');
$data = json_decode($json, true);

// جلب معرف الكاشير
$idCaissier = isset($data['idCaissier']) ? intval($data['idCaissier']) : 0;

// التحقق من وجود معرف صالح
if ($idCaissier <= 0) {
    echo json_encode(['error' => 'Identifiant Caissier invalide ou manquant']);
    exit;
}

try {
    // بداية المعاملة (transaction) لضمان التكاملية
    $pdo->beginTransaction();

    // أولاً: جلب idUtilisateur المرتبط بالكاشير المطلوب تعطيله
    $stmt = $pdo->prepare("SELECT idUtilisateur FROM Caissier WHERE idCaissier = :idCaissier");
    $stmt->bindValue(':idCaissier', $idCaissier, PDO::PARAM_INT);
    $stmt->execute();

    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$result) {
        // إذا لم يتم إيجاد الكاشير
        $pdo->rollBack();
        echo json_encode(['error' => 'Caissier non trouvé']);
        exit;
    }

    $idUtilisateur = $result['idUtilisateur'];

    // تحديث حالة الحساب في جدول Utilisateur إلى "désactivé"
    $stmtUpdate = $pdo->prepare("UPDATE Utilisateur SET etatCompte = 'désactivé' WHERE idUtilisateur = :idUtilisateur");
    $stmtUpdate->bindValue(':idUtilisateur', $idUtilisateur, PDO::PARAM_INT);
    $stmtUpdate->execute();

    // إنهاء المعاملة بنجاح
    $pdo->commit();

    echo json_encode(['success' => 'Compte Caissier désactivé avec succès']);
} catch (PDOException $e) {
    // في حالة وجود خطأ، التراجع عن المعاملة وإرجاع رسالة الخطأ
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    echo json_encode(['error' => 'Erreur base de données : ' . $e->getMessage()]);
}
