<?php
header('Content-Type: application/json; charset=UTF-8');
require_once __DIR__ . '/../commen/db_connection.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // قراءة المعطيات من الواجهة (JSON)
    $input = json_decode(file_get_contents("php://input"), true);

    $typeMoyen = $input['typeMoyen'] ?? '';
    $lieuDepart = $input['lieuDepart'] ?? '';
    $lieuArrivee = $input['lieuArrivee'] ?? '';
    $heureDepart = $input['heureDepart'] ?? '';
    $dateReservation = $input['dateReservation'] ?? '';
    $idPassager = $input['idPassager'] ?? '';

    // التحقق من المعطيات
    if (empty($typeMoyen) || empty($lieuDepart) || empty($lieuArrivee) || empty($heureDepart) || empty($dateReservation) || empty($idPassager)) {
        echo json_encode(["error" => "جميع الحقول مطلوبة."]);
        exit;
    }

    try {
        // البحث عن خدمة نقل توافق المعايير
        $sql = "
            SELECT s.idService
            FROM ServiceDeTransport s
            JOIN Trajet t ON s.idTrajet = t.idTrajet
            JOIN MoyenDeTransport m ON s.idMoyen = m.idMoyen
            WHERE m.type = :typeMoyen
              AND t.lieuDepart = :lieuDepart
              AND t.lieuArrivee = :lieuArrivee
              AND s.heureDepart = :heureDepart
              AND s.etat = 'actif'
            LIMIT 1
        ";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            ':typeMoyen' => $typeMoyen,
            ':lieuDepart' => $lieuDepart,
            ':lieuArrivee' => $lieuArrivee,
            ':heureDepart' => $heureDepart
        ]);

        $service = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$service) {
            echo json_encode(["error" => "لا توجد خدمة نقل توافق المعايير المحددة."]);
            exit;
        }

        $idService = $service['idService'];

        // إدراج الحجز الجديد
        $insertSQL = "
            INSERT INTO Réservation (dateReservation, etat, idPassager, idService, archive)
            VALUES (:dateReservation, 'pending', :idPassager, :idService, 0)
        ";
        $stmtInsert = $pdo->prepare($insertSQL);
        $stmtInsert->execute([
            ':dateReservation' => $dateReservation,
            ':idPassager' => $idPassager,
            ':idService' => $idService
        ]);

        $idReservation = $pdo->lastInsertId();

        // إرجاع كائن الحجز
        echo json_encode([
            "idReservation" => $idReservation,
            "dateReservation" => $dateReservation,
            "etat" => "pending",
            "idPassager" => $idPassager,
            "idService" => $idService,
            "archive" => 0
        ], JSON_UNESCAPED_UNICODE);

    } catch (PDOException $e) {
        echo json_encode(["error" => "خطأ في قاعدة البيانات: " . $e->getMessage()]);
    }
} else {
    echo json_encode(["error" => "الطريقة غير مسموحة."]);
}
