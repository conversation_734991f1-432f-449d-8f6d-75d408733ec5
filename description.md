````markdown
# Web Project 

##  Project Overview
This project is a full-featured web application for managing an online transport booking system. It covers the entire journey lifecycle—from browsing available services to booking, payment, and ticket issuance—through a clean, role-based interface.

Key user roles:

Passengers can search for trips by origin, destination and date; reserve seats; view, modify or cancel their bookings; and manage their profiles.

Drivers receive assigned trips, view schedule details, and update each trip’s status (e.g. “Scheduled,” “In Progress,” “Completed”).

Cashiers review all reservations, mark them as “Paid” or “Unpaid,” and generate payment reports.

Administrators have full control over the system: they activate/deactivate user accounts, reset passwords, and create, update or delete core entities (services, routes, vehicles, drivers, cashiers).


## Architecture and Folder Structure

This project follows the MVC (Model-View-Controller) architecture, with two main root folders:

```
project-root/
├── frontend/
│   ├── model/
│   │   └── (contains class definitions)
│   ├── view/
│   │   ├── signup/         
│   │   │   ├── signup.html
│   │   │   ├── signup.css
│   │   │   └── signup.js
│   │   ├── login/
│   │   │   ├── login.html
│   │   │   └── login.js
│   │   └── ... (other use-case folders)
│   └── controller/
│       ├── signupController.js
│       ├── loginController.js
│       └── ... (other business logic controllers)

├── backend/
│   ├── 4628913_safratydz.sql        
│   └── server/
└──        

```

##  Use Cases

## Actors
- **Visiteur**: Rechercher les services de transport, S’inscrire
- **Passager**: Authentification, Réservation, Gestion du profil, Consultation et annulation des réservations
- **Conducteur**: Consultation des trajets assignés, Mise à jour de l’état des trajets
- **Caissier**: Consultation des réservations, Marquer une réservation comme payée ou non payée
- **Administrateur**: Gestion des comptes utilisateurs, services de transport, trajets, chauffeurs, moyens de transport, caissiers

## Use Case Summaries

### Visiteur
- Rechercher service de transport
- S’inscrire

### Passager
- Se connecter
- Réserver un transport
- Consulter/annuler/modifier une réservation
- Modifier profil/mot de passe

### Conducteur
- Se connecter
- Consulter trajets assignés
- Mettre à jour l’état d’un trajet

### Caissier
- Se connecter
- Consulter réservations
- Marquer une réservation comme payée ou non payée

### Administrateur
- Activer/désactiver comptes utilisateurs
- Réinitialiser mot de passe
- Ajouter/modifier/supprimer/rechercher:
  - Services de transport
  - Trajets
  - Moyens de transport
  - Chauffeurs
  - Caissiers

---

## Additional Commands
* this application will be hosted later on awardspace so make that in count to change the url easly .
* The official language of the application — for all documentation, source code, and user interface — is **French**.
* Use only the following core technologies:

  * **HTML**
  * **CSS**
  * **JavaScript**
