-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Hôte : fdb1030.awardspace.net
-- G<PERSON><PERSON><PERSON> le : mar. 27 mai 2025 à 11:18
-- Version du serveur : 8.0.32
-- Version de PHP : 8.1.32

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de données : `4628913_safratydz`
--

-- --------------------------------------------------------

--
-- Structure de la table `Administrateur`
--

CREATE TABLE `Administrateur` (
  `idAdmin` int NOT NULL,
  `nom` varchar(100) DEFAULT NULL,
  `prenom` varchar(100) DEFAULT NULL,
  `idUtilisateur` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `Caissier`
--

CREATE TABLE `Caissier` (
  `idCaissier` int NOT NULL,
  `idUtilisateur` int NOT NULL,
  `nom` varchar(20) DEFAULT NULL,
  `prenom` varchar(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Déchargement des données de la table `Caissier`
--

INSERT INTO `Caissier` (`idCaissier`, `idUtilisateur`, `nom`, `prenom`) VALUES
(1, 56, 'Samir', 'Bouhired');

-- --------------------------------------------------------

--
-- Structure de la table `Chauffeur`
--

CREATE TABLE `Chauffeur` (
  `idChauffeur` int NOT NULL,
  `nom` varchar(100) DEFAULT NULL,
  `prenom` varchar(100) DEFAULT NULL,
  `numeroPermis` varchar(50) DEFAULT NULL,
  `idUtilisateur` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Déchargement des données de la table `Chauffeur`
--

INSERT INTO `Chauffeur` (`idChauffeur`, `nom`, `prenom`, `numeroPermis`, `idUtilisateur`) VALUES
(1, 'ISLEM', 'BouDDD', 'DZ2034CH-9876', 53),
(2, 'Karim', 'Bouzid', 'DZ2025CH-9876', 54),
(3, 'Ahmed', 'Benkhaled', 'DZ2025CH-0012', 55);

-- --------------------------------------------------------

--
-- Structure de la table `MoyenDeTransport`
--

CREATE TABLE `MoyenDeTransport` (
  `idMoyen` int NOT NULL,
  `type` varchar(50) DEFAULT NULL,
  `capacite` int DEFAULT NULL,
  `matricule` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Déchargement des données de la table `MoyenDeTransport`
--

INSERT INTO `MoyenDeTransport` (`idMoyen`, `type`, `capacite`, `matricule`) VALUES
(1, 'Bus', 50, '1234-XYZ-16'),
(3, 'Bus', 50, 'BUS-2025-A1'),
(4, 'Taxi', 4, 'TAXI-AL-101'),
(5, 'Train', 200, 'TRAIN-N1-01'),
(6, 'Bus', 55, 'BUS-2025-B2'),
(7, 'Taxi', 4, 'TAXI-OR-202'),
(8, 'Train', 180, 'TRAIN-S1-02'),
(9, 'Bus', 60, 'BUS-2025-C3'),
(10, 'Taxi', 4, 'TAXI-CN-303'),
(11, 'Train', 210, 'TRAIN-W2-03'),
(12, 'Bus', 45, 'BUS-2025-D4'),
(13, 'Taxi', 4, 'TAXI-ST-404'),
(14, 'Train', 190, 'TRAIN-E3-04'),
(15, 'Bus', 52, 'BUS-2025-E5'),
(16, 'Taxi', 4, 'TAXI-TZ-505'),
(17, 'Train', 220, 'TRAIN-T4-05'),
(18, 'Bus', 48, 'BUS-2025-F6'),
(19, 'Taxi', 4, 'TAXI-MD-606'),
(20, 'Train', 230, 'TRAIN-M6-06'),
(21, 'Bus', 53, 'BUS-2025-G7'),
(22, 'Taxi', 4, 'TAXI-BL-707');

-- --------------------------------------------------------

--
-- Structure de la table `Passager`
--

CREATE TABLE `Passager` (
  `idPassager` int NOT NULL,
  `nom` varchar(100) DEFAULT NULL,
  `prenom` varchar(100) DEFAULT NULL,
  `idUtilisateur` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Déchargement des données de la table `Passager`
--

INSERT INTO `Passager` (`idPassager`, `nom`, `prenom`, `idUtilisateur`) VALUES
(1, 'Ahmed', 'Bensalem', 58),
(2, 'Adinn', 'Bensami', 59),
(3, 'Benali', 'Yasmine', 60);

-- --------------------------------------------------------

--
-- Structure de la table `Réservation`
--

CREATE TABLE `Réservation` (
  `idReservation` int NOT NULL,
  `dateReservation` date DEFAULT NULL,
  `etat` enum('payé','non payé','pending') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `idPassager` int DEFAULT NULL,
  `idService` int DEFAULT NULL,
  `archive` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Déchargement des données de la table `Réservation`
--

INSERT INTO `Réservation` (`idReservation`, `dateReservation`, `etat`, `idPassager`, `idService`, `archive`) VALUES
(1, '2025-05-27', 'pending', 1, 3, 0),
(2, '2025-05-27', 'pending', 1, 5, 0),
(3, '2025-05-27', 'payé', 2, 2, 0);

-- --------------------------------------------------------

--
-- Structure de la table `ServiceDeTransport`
--

CREATE TABLE `ServiceDeTransport` (
  `idService` int NOT NULL,
  `heureDepart` time DEFAULT NULL,
  `heureArrivee` time DEFAULT NULL,
  `prix` decimal(10,2) DEFAULT NULL,
  `idTrajet` int DEFAULT NULL,
  `idMoyen` int DEFAULT NULL,
  `idChauffeur` int DEFAULT NULL,
  `etat` enum('actif','désactivé') DEFAULT 'actif'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Déchargement des données de la table `ServiceDeTransport`
--

INSERT INTO `ServiceDeTransport` (`idService`, `heureDepart`, `heureArrivee`, `prix`, `idTrajet`, `idMoyen`, `idChauffeur`, `etat`) VALUES
(1, '08:30:00', '12:00:00', 2500.00, 2, 1, 3, 'désactivé'),
(2, '10:00:00', '14:00:00', 3000.00, 4, 1, 2, 'actif'),
(3, '08:30:00', '12:00:00', 3000.00, 21, 5, 1, 'actif'),
(5, '08:30:00', '12:00:00', 3000.00, 21, 3, 2, 'actif');

-- --------------------------------------------------------

--
-- Structure de la table `Trajet`
--

CREATE TABLE `Trajet` (
  `idTrajet` int NOT NULL,
  `lieuDepart` varchar(255) DEFAULT NULL,
  `lieuArrivee` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Déchargement des données de la table `Trajet`
--

INSERT INTO `Trajet` (`idTrajet`, `lieuDepart`, `lieuArrivee`) VALUES
(1, '[Alger]', '[Oran]'),
(2, 'Constantine', 'Annaba'),
(4, 'Béjaïa', 'Sétif'),
(5, 'Batna', 'Biskra'),
(6, 'Tizi Ouzou', 'Alger'),
(7, 'Mostaganem', 'Chlef'),
(8, 'Skikda', 'Guelma'),
(9, 'Laghouat', 'Ghardaïa'),
(10, 'El Oued', 'Ouargla'),
(11, 'Tébessa', 'Khenchela'),
(12, 'Jijel', 'Mila'),
(13, 'Saïda', 'Naâma'),
(14, 'Tiaret', 'Relizane'),
(15, 'Adrar', 'Timimoun'),
(16, 'Illizi', 'Djanet'),
(17, 'Tamanrasset', 'In Salah'),
(18, 'Sidi Bel Abbès', 'Mascara'),
(19, 'Médéa', 'Aïn Defla'),
(20, 'Souk Ahras', 'El Tarf'),
(21, 'Alger', 'Tlemcen');

-- --------------------------------------------------------

--
-- Structure de la table `Utilisateur`
--

CREATE TABLE `Utilisateur` (
  `idUtilisateur` int NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `motDePasse` varchar(255) DEFAULT NULL,
  `role` enum('Administrateur','Chauffeur','passager','Caissier') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `etatCompte` enum('activé','désactivé') DEFAULT 'activé'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Déchargement des données de la table `Utilisateur`
--

INSERT INTO `Utilisateur` (`idUtilisateur`, `email`, `motDePasse`, `role`, `etatCompte`) VALUES
(1, 'h', 'h', 'Chauffeur', 'activé'),
(2, '<EMAIL>', 'password123', 'passager', 'activé'),
(3, '<EMAIL>', 'motdepasse', 'Chauffeur', 'activé'),
(4, '<EMAIL>', '123456', 'Chauffeur', 'activé'),
(5, '<EMAIL>', 'azerty', 'Chauffeur', 'activé'),
(6, '<EMAIL>', 'qwerty', 'passager', 'activé'),
(7, '<EMAIL>', 'pass2025', 'passager', 'activé'),
(8, '<EMAIL>', 'abc123', 'passager', 'activé'),
(9, '<EMAIL>', 'monmotdepass', 'passager', 'activé'),
(10, '<EMAIL>', 'mot123', 'passager', 'activé'),
(11, '<EMAIL>', 'pwdpwd', 'passager', 'activé'),
(12, '<EMAIL>', 'pass@2025', 'passager', 'activé'),
(13, '<EMAIL>', 'mdp2025', 'passager', 'activé'),
(14, '<EMAIL>', 'rafik13', 'passager', 'activé'),
(15, '<EMAIL>', 'nadia!', 'passager', 'activé'),
(16, '<EMAIL>', 'yahia2025', 'passager', 'activé'),
(17, '<EMAIL>', 'fatimaPWD', 'passager', 'activé'),
(18, '<EMAIL>', 'samiLarbi', 'passager', 'activé'),
(19, '<EMAIL>', 'kheira*123', 'passager', 'activé'),
(20, '<EMAIL>', 'secret19', 'passager', 'activé'),
(21, '<EMAIL>', 'yas23mina', 'passager', 'activé'),
(23, 'hقق', 'h', 'passager', 'activé'),
(24, '<EMAIL>', 'hhhh', 'Chauffeur', 'activé'),
(25, '<EMAIL>', 'password123', 'Administrateur', 'activé'),
(26, '<EMAIL>', 'motdepasse', 'Chauffeur', 'activé'),
(27, '<EMAIL>', '123456', 'passager', 'désactivé'),
(28, '<EMAIL>', 'azerty', 'passager', 'activé'),
(53, '<EMAIL>', '$2y$10$7shTTlfkRRvA4.AbA.jcruWmJzCRr1hiMW4NcYnWoHqaO3FVq230q', 'Chauffeur', 'activé'),
(54, '<EMAIL>', '$2y$10$E85KNNpjlG/WrTeH23jC9.tLx5kgIqUIjVYWkIQjgKXoorklCmD26', 'Chauffeur', 'activé'),
(55, '<EMAIL>', '$2y$10$JYDPjEAViaYElwWcPQTTvObi7ETRygEKPf409fWXh6LP.m83u/dx6', 'Chauffeur', 'activé'),
(56, '<EMAIL>', '$2y$10$Nm2FAhhXzB6aN4C5Kmq05.NZCcu1KrYC59oIc3dTF5Q7i5nKbAN6O', 'Caissier', 'désactivé'),
(58, '<EMAIL>', '123456', 'passager', 'activé'),
(59, '<EMAIL>', '123d456', 'passager', 'activé'),
(60, '<EMAIL>', '$2y$10$NvBY68/AumOBjuDp/UP5SOo.VLfczJXGSYiNAmIHch1Rk/DZMftHu', 'passager', 'activé');

--
-- Index pour les tables déchargées
--

--
-- Index pour la table `Administrateur`
--
ALTER TABLE `Administrateur`
  ADD PRIMARY KEY (`idAdmin`),
  ADD KEY `idUtilisateur` (`idUtilisateur`);

--
-- Index pour la table `Caissier`
--
ALTER TABLE `Caissier`
  ADD PRIMARY KEY (`idCaissier`),
  ADD KEY `idUtilisateur` (`idUtilisateur`);

--
-- Index pour la table `Chauffeur`
--
ALTER TABLE `Chauffeur`
  ADD PRIMARY KEY (`idChauffeur`),
  ADD KEY `idUtilisateur` (`idUtilisateur`);

--
-- Index pour la table `MoyenDeTransport`
--
ALTER TABLE `MoyenDeTransport`
  ADD PRIMARY KEY (`idMoyen`);

--
-- Index pour la table `Passager`
--
ALTER TABLE `Passager`
  ADD PRIMARY KEY (`idPassager`),
  ADD KEY `idUtilisateur` (`idUtilisateur`);

--
-- Index pour la table `Réservation`
--
ALTER TABLE `Réservation`
  ADD PRIMARY KEY (`idReservation`),
  ADD KEY `idPassager` (`idPassager`),
  ADD KEY `idService` (`idService`);

--
-- Index pour la table `ServiceDeTransport`
--
ALTER TABLE `ServiceDeTransport`
  ADD PRIMARY KEY (`idService`),
  ADD KEY `idTrajet` (`idTrajet`),
  ADD KEY `idMoyen` (`idMoyen`),
  ADD KEY `idChauffeur` (`idChauffeur`);

--
-- Index pour la table `Trajet`
--
ALTER TABLE `Trajet`
  ADD PRIMARY KEY (`idTrajet`);

--
-- Index pour la table `Utilisateur`
--
ALTER TABLE `Utilisateur`
  ADD PRIMARY KEY (`idUtilisateur`);

--
-- AUTO_INCREMENT pour les tables déchargées
--

--
-- AUTO_INCREMENT pour la table `Caissier`
--
ALTER TABLE `Caissier`
  MODIFY `idCaissier` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT pour la table `Chauffeur`
--
ALTER TABLE `Chauffeur`
  MODIFY `idChauffeur` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT pour la table `MoyenDeTransport`
--
ALTER TABLE `MoyenDeTransport`
  MODIFY `idMoyen` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=23;

--
-- AUTO_INCREMENT pour la table `Passager`
--
ALTER TABLE `Passager`
  MODIFY `idPassager` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT pour la table `Réservation`
--
ALTER TABLE `Réservation`
  MODIFY `idReservation` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT pour la table `ServiceDeTransport`
--
ALTER TABLE `ServiceDeTransport`
  MODIFY `idService` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT pour la table `Trajet`
--
ALTER TABLE `Trajet`
  MODIFY `idTrajet` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT pour la table `Utilisateur`
--
ALTER TABLE `Utilisateur`
  MODIFY `idUtilisateur` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=61;

--
-- Contraintes pour les tables déchargées
--

--
-- Contraintes pour la table `Administrateur`
--
ALTER TABLE `Administrateur`
  ADD CONSTRAINT `Administrateur_ibfk_1` FOREIGN KEY (`idUtilisateur`) REFERENCES `Utilisateur` (`idUtilisateur`);

--
-- Contraintes pour la table `Chauffeur`
--
ALTER TABLE `Chauffeur`
  ADD CONSTRAINT `Chauffeur_ibfk_1` FOREIGN KEY (`idUtilisateur`) REFERENCES `Utilisateur` (`idUtilisateur`);

--
-- Contraintes pour la table `Passager`
--
ALTER TABLE `Passager`
  ADD CONSTRAINT `Passager_ibfk_1` FOREIGN KEY (`idUtilisateur`) REFERENCES `Utilisateur` (`idUtilisateur`);

--
-- Contraintes pour la table `Réservation`
--
ALTER TABLE `Réservation`
  ADD CONSTRAINT `Réservation_ibfk_1` FOREIGN KEY (`idPassager`) REFERENCES `Passager` (`idPassager`) ON DELETE SET NULL,
  ADD CONSTRAINT `Réservation_ibfk_2` FOREIGN KEY (`idService`) REFERENCES `ServiceDeTransport` (`idService`) ON DELETE SET NULL;

--
-- Contraintes pour la table `ServiceDeTransport`
--
ALTER TABLE `ServiceDeTransport`
  ADD CONSTRAINT `ServiceDeTransport_ibfk_1` FOREIGN KEY (`idTrajet`) REFERENCES `Trajet` (`idTrajet`) ON DELETE SET NULL,
  ADD CONSTRAINT `ServiceDeTransport_ibfk_2` FOREIGN KEY (`idMoyen`) REFERENCES `MoyenDeTransport` (`idMoyen`) ON DELETE SET NULL,
  ADD CONSTRAINT `ServiceDeTransport_ibfk_3` FOREIGN KEY (`idChauffeur`) REFERENCES `Chauffeur` (`idChauffeur`) ON DELETE SET NULL;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
