    
<?php
// commen/db_connection.php
// إعداد اتصال PDO بقاعدة البيانات

// بيانات الاتصال
$host = "fdb1030.awardspace.net";
$db   = "4628913_safratydz";
$user = "4628913_safratydz";
$pass = "@Safratyhadilayaproject2025";   
$charset = 'utf8mb4';

$dsn = "mysql:host=$host;dbname=$db;charset=$charset";
$options = [
    PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES   => false,
];

try {
    // إنشاء كائن PDO
    $pdo = new PDO($dsn, $user, $pass, $options);
} catch (PDOException $e) {
    // في حالة الخطأ، اعرض رسالة وانهِ التنفيذ
    http_response_code(500);
    echo json_encode(["error" => "Database Connection Failed: " . $e->getMessage()]);
    exit;
}
