<?php
header('Content-Type: application/json; charset=UTF-8');
require_once __DIR__ . '/../commen/db_connection.php';

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // التأكد من وجود idPassager في الرابط
    if (!isset($_GET['idPassager'])) {
        echo json_encode(["error" => "يرجى تحديد معرف الراكب (idPassager)"]);
        exit;
    }

    $idPassager = $_GET['idPassager'];

    try {
        // جلب كل الحجوزات المرتبطة بهذا الراكب
        $sql = "
            SELECT 
                r.idReservation,
                r.dateReservation,
                r.etat,
                r.archive,
                s.idService,
                s.heureDepart,
                s.heureArrivee,
                s.prix,
                t.lieuDepart,
                t.lieuArrivee,
                m.type AS typeMoyen
            FROM Réservation r
            JOIN ServiceDeTransport s ON r.idService = s.idService
            JOIN Trajet t ON s.idTrajet = t.idTrajet
            JOIN MoyenDeTransport m ON s.idMoyen = m.idMoyen
            WHERE r.idPassager = :idPassager
            ORDER BY r.dateReservation DESC
        ";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([':idPassager' => $idPassager]);

        $reservations = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode($reservations, JSON_UNESCAPED_UNICODE);
    } catch (PDOException $e) {
        echo json_encode(["error" => "خطأ في قاعدة البيانات: " . $e->getMessage()]);
    }

} else {
    echo json_encode(["error" => "الطريقة غير مسموحة، استخدم GET فقط."]);
}
