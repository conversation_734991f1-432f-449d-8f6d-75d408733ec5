<?php
header('Content-Type: application/json; charset=UTF-8');
require_once __DIR__ . '/../commen/db_connection.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents("php://input"), true);
    $idReservation = $input['idReservation'] ?? null;

    if (!$idReservation) {
        echo json_encode(["error" => "يرجى تقديم معرف الحجز (idReservation)"]);
        exit;
    }

    try {
        // التحقق من وجود الحجز أولاً
        $checkStmt = $pdo->prepare("SELECT * FROM Réservation WHERE idReservation = :idReservation");
        $checkStmt->execute([':idReservation' => $idReservation]);

        if ($checkStmt->rowCount() === 0) {
            echo json_encode(["error" => "الحجز غير موجود"]);
            exit;
        }

        // تعديل قيمة archive إلى 1 (حذف منطقي)
        $sql = "UPDATE Réservation SET archive = 1 WHERE idReservation = :idReservation";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([':idReservation' => $idReservation]);

        echo json_encode(["message" => "تم حذف الحجز (منطقياً) بنجاح", "idReservation" => $idReservation]);
    } catch (PDOException $e) {
        echo json_encode(["error" => "خطأ في قاعدة البيانات: " . $e->getMessage()]);
    }

} else {
    echo json_encode(["error" => "الطريقة غير مسموحة، استخدم POST فقط."]);
}
