<?php
header('Content-Type: application/json; charset=UTF-8');
require_once __DIR__ . '/../commen/db_connection.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // قراءة البيانات القادمة من الواجهة الأمامية بصيغة JSON
    $input = json_decode(file_get_contents("php://input"), true);

    $idReservation = $input['idReservation'] ?? null;
    $etat = $input['etat'] ?? null;

    // التحقق من البيانات
    if (!$idReservation || !$etat) {
        echo json_encode(["error" => "يرجى تقديم معرف الحجز وحالة الدفع الجديدة"]);
        exit;
    }

    // التحقق من أن القيمة المدخلة للحالة صحيحة
    $allowedEtats = ['payé', 'non payé'];
    if (!in_array($etat, $allowedEtats)) {
        echo json_encode(["error" => "قيمة etat غير صالحة. يجب أن تكون 'payé' أو 'non payé'"]);
        exit;
    }

    try {
        // التحقق من وجود الحجز
        $check = $pdo->prepare("SELECT * FROM Réservation WHERE idReservation = :id");
        $check->execute([':id' => $idReservation]);

        if ($check->rowCount() === 0) {
            echo json_encode(["error" => "الحجز غير موجود"]);
            exit;
        }

        // تحديث حالة الحجز
        $update = $pdo->prepare("UPDATE Réservation SET etat = :etat WHERE idReservation = :id");
        $update->execute([
            ':etat' => $etat,
            ':id' => $idReservation
        ]);

        echo json_encode(["message" => "تم تحديث حالة الحجز بنجاح", "idReservation" => $idReservation, "etat" => $etat]);

    } catch (PDOException $e) {
        echo json_encode(["error" => "خطأ في قاعدة البيانات: " . $e->getMessage()]);
    }

} else {
    echo json_encode(["error" => "الطريقة غير مسموحة. يجب استخدام POST"]);
}
