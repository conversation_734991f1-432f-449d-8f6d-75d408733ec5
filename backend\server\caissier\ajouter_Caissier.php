<?php
// 1. إعداد ترويسة JSON
header('Content-Type: application/json; charset=UTF-8');

// 2. تضمين الاتصال بقاعدة البيانات
require_once __DIR__ . '/../commen/db_connection.php';

// 3. قراءة البيانات القادمة من الواجهة بصيغة JSON
$input = json_decode(file_get_contents('php://input'), true);

// 4. استخراج القيم الأساسية
$nom = $input['nom'] ?? null;
$prenom = $input['prenom'] ?? null;
$email = $input['email'] ?? null;
$motDePasse = $input['motDePasse'] ?? null;
$role = $input['role'] ?? null;  // يجب أن تكون القيمة "Caissier"

// التحقق من وجود جميع الحقول المطلوبة
if (!$nom || !$prenom || !$email || !$motDePasse || $role !== 'Caissier') {
    http_response_code(400);
    echo json_encode(['error' => 'Données manquantes ou rôle invalide.']);
    exit;
}

try {
    // 5. تشفير كلمة المرور
    $hashedPassword = password_hash($motDePasse, PASSWORD_DEFAULT);

    // 6. إدخال المستخدم في جدول Utilisateur
    $stmt = $pdo->prepare("INSERT INTO Utilisateur (email, motDePasse, role, etatCompte) VALUES (?, ?, ?, 'activé')");
    $stmt->execute([$email, $hashedPassword, $role]);

    // 7. الحصول على ID الخاص بالمستخدم الجديد
    $idUtilisateur = $pdo->lastInsertId();

    // 8. إدخال البيانات في جدول Caissier
    $stmt2 = $pdo->prepare("INSERT INTO Caissier (nom, prenom, idUtilisateur) VALUES (?, ?, ?)");
    $stmt2->execute([$nom, $prenom, $idUtilisateur]);

    // 9. إعداد الرد بصيغة JSON
    $response = [
        'idCaissier' => $pdo->lastInsertId(),
        'nom' => $nom,
        'prenom' => $prenom,
        'email' => $email,
        'role' => $role,
        'etatCompte' => 'activé'
    ];

    echo json_encode($response);

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erreur serveur : ' . $e->getMessage()]);
}
?>
