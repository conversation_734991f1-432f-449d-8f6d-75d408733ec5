<?php
header('Content-Type: application/json; charset=UTF-8');
require_once __DIR__ . '/../commen/db_connection.php';

$json = file_get_contents("php://input");
$data = json_decode($json, true);
$idMoyen = isset($data['idMoyen']) ? intval($data['idMoyen']) : 0;

if ($idMoyen <= 0) {
    echo json_encode(['error' => 'ID invalide']);
    exit;
}

try {
    $sql = "DELETE FROM MoyenDeTransport WHERE idMoyen = :idMoyen";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':idMoyen', $idMoyen, PDO::PARAM_INT);
    $stmt->execute();

    echo json_encode(['message' => 'Moyen de transport supprimé avec succès']);
} catch (PDOException $e) {
    echo json_encode(['error' => 'Erreur lors de la suppression : ' . $e->getMessage()]);
}
?>
