<?php
// إعداد ترويسة JSON
header('Content-Type: application/json; charset=UTF-8');

// الاتصال بقاعدة البيانات
require_once __DIR__ . '/../commen/db_connection.php';

// التحقق من أن الطلب من نوع POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {

    // جلب البيانات من جسم الطلب بصيغة JSON
    $data = json_decode(file_get_contents("php://input"), true);

    // التحقق من وجود البيانات المطلوبة
    $lieuDepart = trim($data['lieuDepart'] ?? '');
    $lieuArrivee = trim($data['lieuArrivee'] ?? '');

    if ($lieuDepart === '' || $lieuArrivee === '') {
        echo json_encode(["error" => "Les champs 'lieuDepart' et 'lieuArrivee' sont requis."]);
        exit;
    }

    try {
        // تنفيذ عملية الإدخال في جدول Trajet
        $stmt = $pdo->prepare("INSERT INTO Trajet (lieuDepart, lieuArrivee) VALUES (?, ?)");
        $stmt->execute([$lieuDepart, $lieuArrivee]);

        // الحصول على ID الرحلة المدخلة
        $idTrajet = $pdo->lastInsertId();

        // إنشاء كائن الرحلة للإرجاع
        $trajet = [
            "idTrajet" => $idTrajet,
            "lieuDepart" => $lieuDepart,
            "lieuArrivee" => $lieuArrivee
        ];

        echo json_encode($trajet);

    } catch (PDOException $e) {
        echo json_encode(["error" => "Erreur lors de l'ajout: " . $e->getMessage()]);
    }

} else {
    echo json_encode(["error" => "Méthode non autorisée"]);
}
?>
