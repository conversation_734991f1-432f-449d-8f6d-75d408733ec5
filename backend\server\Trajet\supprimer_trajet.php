<?php
// ترويسة النوع JSON
header('Content-Type: application/json; charset=UTF-8');

// الاتصال بقاعدة البيانات
require_once __DIR__ . '/../commen/db_connection.php';

// التأكد أن الطلب من نوع POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(["error" => "Méthode non autorisée"]);
    exit;
}

// قراءة البيانات من الطلب
$data = json_decode(file_get_contents("php://input"), true);

// استخراج idTrajet
$idTrajet = isset($data['idTrajet']) ? intval($data['idTrajet']) : 0;

if ($idTrajet <= 0) {
    echo json_encode(["error" => "ID du trajet invalide."]);
    exit;
}

try {
    // تنفيذ الحذف
    $stmt = $pdo->prepare("DELETE FROM Trajet WHERE idTrajet = ?");
    $stmt->execute([$idTrajet]);

    if ($stmt->rowCount() > 0) {
        echo json_encode(["success" => "Le trajet a été supprimé avec succès."]);
    } else {
        echo json_encode(["error" => "Aucun trajet trouvé avec cet ID."]);
    }

} catch (PDOException $e) {
    echo json_encode(["error" => "Erreur base de données : " . $e->getMessage()]);
}
