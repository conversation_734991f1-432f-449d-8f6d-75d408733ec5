<?php
header('Content-Type: application/json');
require_once __DIR__ . '/../commen/db_connection.php';

$response = [];

if ($_SERVER["REQUEST_METHOD"] === "POST") {
    $data = json_decode(file_get_contents("php://input"), true);

    $idChauffeur = $data['idChauffeur'] ?? null;
    $nom = $data['nom'] ?? '';
    $prenom = $data['prenom'] ?? '';
    $numeroPermis = $data['numeroPermis'] ?? '';
    $email = $data['email'] ?? '';
    $motDePasse = $data['motDePasse'] ?? '';
    $etatCompte = $data['etatCompte'] ?? '';

    if (!$idChauffeur) {
        echo json_encode(['error' => 'idChauffeur est requis']);
        exit;
    }

    try {
        // 1. الحصول على idUtilisateur المرتبط بالسائق
        $stmt1 = $pdo->prepare("SELECT idUtilisateur FROM Chauffeur WHERE idChauffeur = ?");
        $stmt1->execute([$idChauffeur]);
        $chauffeur = $stmt1->fetch();

        if (!$chauffeur) {
            echo json_encode(['error' => 'Chauffeur non trouvé']);
            exit;
        }

        $idUtilisateur = $chauffeur['idUtilisateur'];

        // 2. تحديث جدول Chauffeur
        $stmt2 = $pdo->prepare("UPDATE Chauffeur SET nom = ?, prenom = ?, numeroPermis = ? WHERE idChauffeur = ?");
        $stmt2->execute([$nom, $prenom, $numeroPermis, $idChauffeur]);

        // 3. تحديث جدول Utilisateur (email، motDePasse، etatCompte)
        if (!empty($motDePasse)) {
            $motDePasseHashe = password_hash($motDePasse, PASSWORD_BCRYPT);
            $stmt3 = $pdo->prepare("UPDATE Utilisateur SET email = ?, motDePasse = ?, etatCompte = ? WHERE idUtilisateur = ?");
            $stmt3->execute([$email, $motDePasseHashe, $etatCompte, $idUtilisateur]);
        } else {
            $stmt3 = $pdo->prepare("UPDATE Utilisateur SET email = ?, etatCompte = ? WHERE idUtilisateur = ?");
            $stmt3->execute([$email, $etatCompte, $idUtilisateur]);
        }

        $response = ["success" => true, "message" => "Chauffeur mis à jour avec succès."];
    } catch (PDOException $e) {
        $response = ["error" => "Erreur: " . $e->getMessage()];
    }
} else {
    $response = ["error" => "Méthode non autorisée"];
}

echo json_encode($response);
?>
