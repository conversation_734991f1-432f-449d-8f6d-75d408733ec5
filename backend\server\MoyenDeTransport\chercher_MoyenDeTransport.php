<?php
header('Content-Type: application/json; charset=UTF-8');
require_once __DIR__ . '/../commen/db_connection.php';

$json = file_get_contents("php://input");
$data = json_decode($json, true);

$query = isset($data["query"]) ? $data["query"] : '';

if (trim($query) === '') {
    echo json_encode([]);
    exit;
}

$sql = "
    SELECT * FROM MoyenDeTransport
    WHERE type LIKE :typeQuery OR matricule LIKE :matriculeQuery
    ORDER BY type ASC
";

$stmt = $pdo->prepare($sql);
$likeQuery = '%' . $query . '%';
$stmt->bindValue(':typeQuery', $likeQuery);
$stmt->bindValue(':matriculeQuery', $likeQuery);
$stmt->execute();

$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
echo json_encode($results);
?>
