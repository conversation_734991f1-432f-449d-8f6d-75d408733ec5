<?php
header('Content-Type: application/json; charset=UTF-8');
require_once __DIR__ . '/../commen/db_connection.php';

try {
    // استعلام لجلب كل خدمات النقل مع التفاصيل المرتبطة
    $sql = "
        SELECT 
            s.idService,
            s.heureDepart,
            s.heureArrivee,
            s.prix,
            s.etat,
            t.lieuDepart,
            t.lieuArrivee,
            c.nom AS nomChauffeur,
            c.prenom AS prenomChauffeur,
            m.type AS typeMoyen,
            m.matricule AS matriculeMoyen
        FROM ServiceDeTransport s
        JOIN Trajet t ON s.idTrajet = t.idTrajet
        JOIN MoyenDeTransport m ON s.idMoyen = m.idMoyen
        JOIN Chauffeur c ON s.idChauffeur = c.idChauffeur
        JOIN Utilisateur u ON c.idUtilisateur = u.idUtilisateur
    ";

    $stmt = $pdo->query($sql);
    $services = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode($services, JSON_UNESCAPED_UNICODE);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(["error" => "Erreur base de données : " . $e->getMessage()]);
}
