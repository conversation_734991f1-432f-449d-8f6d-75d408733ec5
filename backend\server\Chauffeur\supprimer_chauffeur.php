<?php
// تحديد نوع المحتوى كـ JSON
header('Content-Type: application/json; charset=UTF-8');

// تضمين الاتصال بقاعدة البيانات
require_once __DIR__ . '/../commen/db_connection.php';

// التحقق من أن الطلب من نوع POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // قراءة البيانات المرسلة في جسم الطلب بصيغة JSON
    $data = json_decode(file_get_contents("php://input"), true);

    // التحقق من وجود idUtilisateur
    if (!isset($data['idUtilisateur'])) {
        echo json_encode(["error" => "Paramètre manquant : idUtilisateur"]);
        exit;
    }

    $idUtilisateur = $data['idUtilisateur'];

    try {
        // تنفيذ عملية التحديث لتعطيل الحساب
        $stmt = $pdo->prepare("UPDATE Utilisateur SET etatCompte = 'désactivé' WHERE idUtilisateur = ?");
        $stmt->execute([$idUtilisateur]);

        // التحقق من عدد الصفوف المتأثرة
        if ($stmt->rowCount() > 0) {
            echo json_encode(["success" => "Le compte du chauffeur a été désactivé."]);
        } else {
            echo json_encode(["error" => "Aucun utilisateur trouvé avec cet ID."]);
        }

    } catch (PDOException $e) {
        echo json_encode(["error" => "Erreur lors de la désactivation : " . $e->getMessage()]);
    }
} else {
    // إذا لم يكن الطلب من نوع POST
    echo json_encode(["error" => "Méthode non autorisée"]);
}
?>
