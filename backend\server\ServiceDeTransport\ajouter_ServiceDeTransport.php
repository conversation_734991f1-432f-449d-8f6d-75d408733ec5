<?php
header('Content-Type: application/json; charset=UTF-8');
require_once __DIR__ . '/../commen/db_connection.php';

// قراءة البيانات المرسلة من الواجهة (JSON)
$json = file_get_contents("php://input");
$data = json_decode($json, true);

// استخراج القيم
$heureDepart = $data['heureDepart'] ?? null;
$heureArrivee = $data['heureArrivee'] ?? null;
$prix = $data['prix'] ?? null;
$idTrajet = $data['idTrajet'] ?? null;
$idMoyen = $data['idMoyen'] ?? null;
$idChauffeur = $data['idChauffeur'] ?? null;

// التحقق من أن جميع البيانات مطلوبة موجودة
if (!$heureDepart || !$heureArrivee || !$prix || !$idTrajet || !$idMoyen || !$idChauffeur) {
    echo json_encode(["error" => "Données manquantes."]);
    exit;
}

try {
    // إعداد الاستعلام
    $sql = "INSERT INTO ServiceDeTransport (heureDepart, heureArrivee, prix, idTrajet, idMoyen, idChauffeur)
            VALUES (:heureDepart, :heureArrivee, :prix, :idTrajet, :idMoyen, :idChauffeur)";
    $stmt = $pdo->prepare($sql);

    // ربط القيم
    $stmt->bindParam(':heureDepart', $heureDepart);
    $stmt->bindParam(':heureArrivee', $heureArrivee);
    $stmt->bindParam(':prix', $prix);
    $stmt->bindParam(':idTrajet', $idTrajet);
    $stmt->bindParam(':idMoyen', $idMoyen);
    $stmt->bindParam(':idChauffeur', $idChauffeur);

    // تنفيذ الإدخال
    $stmt->execute();

    // جلب الـ ID الجديد
    $idService = $pdo->lastInsertId();

    // إرجاع النتيجة
    echo json_encode([
        "idService" => $idService,
        "heureDepart" => $heureDepart,
        "heureArrivee" => $heureArrivee,
        "prix" => $prix,
        "idTrajet" => $idTrajet,
        "idMoyen" => $idMoyen,
        "idChauffeur" => $idChauffeur
    ]);

} catch (PDOException $e) {
    echo json_encode(["error" => "Erreur base de données : " . $e->getMessage()]);
}
