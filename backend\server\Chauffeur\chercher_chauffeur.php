<?php
// تضمين ملف الاتصال بقاعدة البيانات عبر PDO
header('Content-Type: application/json');
require_once __DIR__ . '/../commen/db_connection.php';

// ضبط ترويسة الاستجابة لتكون من نوع JSON
header('Content-Type: application/json; charset=UTF-8');

// قراءة البيانات الواردة كـ RAW من طلب POST (منفذ php://input)
$json = file_get_contents('php://input');

// تحويل نص JSON إلى مصفوفة PHP
$data = json_decode($json, true);

// استخراج عبارة البحث من المصفوفة أو تعيينها كفراغ إذا لم توجد
$query = isset($data['query']) ? $data['query'] : '';

// إذا كانت عبارة البحث فارغة أو تتكون من فراغات فقط، نُعيد مصفوفة فارغة وننهي السكربت
if (trim($query) === '') {
    echo json_encode([]);
    exit;
}

// إعداد استعلام SQL محضّر للبحث في جدول Chauffeur المرتبط بجدول Utilisateur
$sql = "
    SELECT c.idChauffeur, c.nom, c.prenom, c.numeroPermis, u.email, u.etatCompte
    FROM Chauffeur AS c
    JOIN Utilisateur AS u ON c.idUtilisateur = u.idUtilisateur
    WHERE (c.nom LIKE :nom OR c.prenom LIKE :prenom OR u.email LIKE :email)
    ORDER BY c.nom ASC
";
$stmt = $pdo->prepare($sql);

// إضافة رمز % لعبارة البحث لتعزيز البحث الجزئي باستخدام LIKE
$likeQuery = "%{$query}%";

// ربط القيم بالمعاملات الناميّة في الاستعلام (مع استخدام أسماء مختلفة لتفادي تكرار المعامل:contentReference[oaicite:6]{index=6})
$stmt->bindValue(':nom', $likeQuery);
$stmt->bindValue(':prenom', $likeQuery);
$stmt->bindValue(':email', $likeQuery);

// تنفيذ الاستعلام المحضّر
$stmt->execute();

// جلب جميع الصفوف الناتجة كمصفوفة مفهرسة بالقيم
// (في حال عدم وجود نتائج ستعود fetchAll بمصفوفة فارغة تلقائيًا:contentReference[oaicite:7]{index=7})
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);

// إعادة النتائج على شكل JSON
echo json_encode($results);
?>
