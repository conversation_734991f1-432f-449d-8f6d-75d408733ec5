<?php
header('Content-Type: application/json; charset=UTF-8');
require_once __DIR__ . '/../commen/db_connection.php';

$json = file_get_contents('php://input');
$data = json_decode($json, true);

$query = isset($data['query']) ? trim($data['query']) : '';

if ($query === '') {
    echo json_encode([]);
    exit;
}

$sql = "
    SELECT c.idCaissier, c.nom, c.prenom, u.email, u.etatCompte
    FROM Caissier AS c
    JOIN Utilisateur AS u ON c.idUtilisateur = u.idUtilisateur
    WHERE c.nom LIKE :search1 OR c.prenom LIKE :search2 OR u.email LIKE :search3
    ORDER BY c.nom ASC
";

$stmt = $pdo->prepare($sql);

$searchTerm = '%' . $query . '%';

$stmt->bindValue(':search1', $searchTerm, PDO::PARAM_STR);
$stmt->bindValue(':search2', $searchTerm, PDO::PARAM_STR);
$stmt->bindValue(':search3', $searchTerm, PDO::PARAM_STR);

try {
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo json_encode($results);
} catch (PDOException $e) {
    echo json_encode(['error' => 'Erreur base de données : ' . $e->getMessage()]);
}
