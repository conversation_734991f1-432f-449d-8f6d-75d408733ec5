<?php
// chercher_ServiceDeTransport.php

header("Content-Type: application/json; charset=UTF-8");
require_once __DIR__ . '/../commen/db_connection.php';

try {
    // الاستعلام الأساسي
    $sql = "
        SELECT 
            s.idService,
            s.heureDepart,
            s.heureArrivee,
            s.prix,
            t.lieuDepart,
            t.lieuArrivee,
            c.nom AS nomChauffeur,
            c.prenom AS prenomChauffeur,
            m.type AS typeMoyen,
            m.matricule AS matriculeMoyen
        FROM ServiceDeTransport s
        LEFT JOIN Trajet t ON s.idTrajet = t.idTrajet
        LEFT JOIN Chauffeur c ON s.idChauffeur = c.idChauffeur
        LEFT JOIN MoyenDeTransport m ON s.idMoyen = m.idMoyen
    ";

    $conditions = [];
    $params = [];

    // تحديد معيار البحث
    if (!empty($_GET['lieuDepart'])) {
        $conditions[] = "t.lieuDepart LIKE :lieuDepart";
        $params[':lieuDepart'] = "%" . $_GET['lieuDepart'] . "%";
    } elseif (!empty($_GET['lieuArrivee'])) {
        $conditions[] = "t.lieuArrivee LIKE :lieuArrivee";
        $params[':lieuArrivee'] = "%" . $_GET['lieuArrivee'] . "%";
    } elseif (!empty($_GET['heureDepart'])) {
        $conditions[] = "s.heureDepart LIKE :heureDepart";
        $params[':heureDepart'] = "%" . $_GET['heureDepart'] . "%";
    } elseif (!empty($_GET['idChauffeur'])) {
        $conditions[] = "s.idChauffeur = :idChauffeur";
        $params[':idChauffeur'] = $_GET['idChauffeur'];
    } elseif (!empty($_GET['idMoyen'])) {
        $conditions[] = "s.idMoyen = :idMoyen";
        $params[':idMoyen'] = $_GET['idMoyen'];
    }

    if (count($conditions) > 0) {
        $sql .= " WHERE " . implode(" AND ", $conditions);
    }

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);

    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo json_encode($results, JSON_UNESCAPED_UNICODE);

} catch (PDOException $e) {
    echo json_encode(["error" => "Erreur base de données : " . $e->getMessage()]);
}
