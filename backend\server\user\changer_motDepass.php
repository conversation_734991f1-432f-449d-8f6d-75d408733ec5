<?php
header('Content-Type: application/json; charset=UTF-8');
require_once __DIR__ . '/../commen/db_connection.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(["error" => "Méthode non autorisée"]);
    exit;
}

// استقبال البيانات بصيغة JSON
$data = json_decode(file_get_contents('php://input'), true);

$email = $data['email'] ?? null;
$oldPassword = $data['oldPassword'] ?? null;
$newPassword = $data['newPassword'] ?? null;

if (!$email || !$oldPassword || !$newPassword) {
    http_response_code(400);
    echo json_encode(["error" => "Tous les champs sont requis"]);
    exit;
}

try {
    // جلب المستخدم
    $stmt = $pdo->prepare("SELECT idUtilisateur, motDePasse FROM Utilisateur WHERE email = :email");
    $stmt->execute(['email' => $email]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$user) {
        http_response_code(404);
        echo json_encode(["error" => "Utilisateur non trouvé"]);
        exit;
    }

    // التحقق من كلمة المرور القديمة
    if (!password_verify($oldPassword, $user['motDePasse'])) {
        http_response_code(401);
        echo json_encode(["error" => "Ancien mot de passe incorrect"]);
        exit;
    }

    // تشفير كلمة المرور الجديدة
    $hashedNewPassword = password_hash($newPassword, PASSWORD_DEFAULT);

    // تحديث كلمة المرور في قاعدة البيانات
    $updateStmt = $pdo->prepare("UPDATE Utilisateur SET motDePasse = :newPass WHERE idUtilisateur = :id");
    $updateStmt->execute([
        ':newPass' => $hashedNewPassword,
        ':id' => $user['idUtilisateur']
    ]);

    echo json_encode(["message" => "Mot de passe mis à jour avec succès"]);

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(["error" => "Erreur base de données : " . $e->getMessage()]);
}
