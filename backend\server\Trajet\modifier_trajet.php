<?php
require_once __DIR__ . '/../commen/db_connection.php';
header('Content-Type: application/json; charset=UTF-8');

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $idTrajet = isset($_GET['idTrajet']) ? intval($_GET['idTrajet']) : 0;

    if ($idTrajet <= 0) {
        echo json_encode(["error" => "idTrajet invalide"]);
        exit;
    }

    try {
        $stmt = $pdo->prepare("SELECT * FROM Trajet WHERE idTrajet = ?");
        $stmt->execute([$idTrajet]);
        $trajet = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($trajet) {
            echo json_encode($trajet);
        } else {
            echo json_encode(["error" => "Aucun trajet trouvé avec id = $idTrajet"]);
        }
    } catch (PDOException $e) {
        echo json_encode(["error" => $e->getMessage()]);
    }

} elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = json_decode(file_get_contents("php://input"), true);

    $idTrajet = intval($data['idTrajet'] ?? 0);
    $lieuDepart = trim($data['lieuDepart'] ?? '');
    $lieuArrivee = trim($data['lieuArrivee'] ?? '');

    if ($idTrajet <= 0 || $lieuDepart === '' || $lieuArrivee === '') {
        echo json_encode(["error" => "Données incomplètes pour mise à jour"]);
        exit;
    }

    try {
        $stmt = $pdo->prepare("UPDATE Trajet SET lieuDepart = ?, lieuArrivee = ? WHERE idTrajet = ?");
        $stmt->execute([$lieuDepart, $lieuArrivee, $idTrajet]);

        if ($stmt->rowCount() > 0) {
            echo json_encode([
                "idTrajet" => $idTrajet,
                "lieuDepart" => $lieuDepart,
                "lieuArrivee" => $lieuArrivee,
                "message" => "Mise à jour réussie"
            ]);
        } else {
            echo json_encode(["message" => "Aucune mise à jour (soit aucune modification, soit id inexistant)"]);
        }
    } catch (PDOException $e) {
        echo json_encode(["error" => $e->getMessage()]);
    }

} else {
    echo json_encode(["error" => "Méthode non autorisée"]);
}
