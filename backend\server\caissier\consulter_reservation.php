<?php
header('Content-Type: application/json; charset=UTF-8');
require_once __DIR__ . '/../commen/db_connection.php';

try {
    $sql = "
        SELECT 
            r.idReservation,
            r.dateReservation,
            r.etat,
            r.archive,
            p.nom AS nomPassager,
            p.prenom AS prenomPassager,
            t.lieuDepart,
            t.lieuArrivee,
            s.heureDepart,
            s.heureArrivee,
            s.prix,
            m.type AS typeMoyen
        FROM Réservation r
        LEFT JOIN Passager p ON r.idPassager = p.idPassager
        LEFT JOIN Utilisateur u ON p.idUtilisateur = u.idUtilisateur
        LEFT JOIN ServiceDeTransport s ON r.idService = s.idService
        LEFT JOIN Trajet t ON s.idTrajet = t.idTrajet
        LEFT JOIN MoyenDeTransport m ON s.idMoyen = m.idMoyen
        ORDER BY r.idReservation DESC
    ";

    $stmt = $pdo->query($sql);
    $reservations = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode($reservations, JSON_UNESCAPED_UNICODE);

} catch (PDOException $e) {
    echo json_encode(["error" => "Erreur base de données : " . $e->getMessage()]);
}
