<?php
header('Content-Type: application/json; charset=UTF-8');
require_once __DIR__ . '/../commen/db_connection.php';

$json = file_get_contents("php://input");
$data = json_decode($json, true);

$search = isset($data['query']) ? trim($data['query']) : '';

if ($search === '') {
    echo json_encode([]);
    exit;
}

try {
    // استخدم معاملين مختلفين :lieuDepart و :lieuArrivee
    $sql = "
        SELECT idTrajet, lieuDepart, lieuArrivee
        FROM Trajet
        WHERE lieuDepart LIKE :lieuDepart OR lieuArrivee LIKE :lieuArrivee
        ORDER BY lieuDepart ASC
    ";

    $stmt = $pdo->prepare($sql);

    $likeSearch = '%' . $search . '%';
    $stmt->bindValue(':lieuDepart', $likeSearch, PDO::PARAM_STR);
    $stmt->bindValue(':lieuArrivee', $likeSearch, PDO::PARAM_STR);

    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode($results);

} catch (PDOException $e) {
    echo json_encode(["error" => "Erreur base de données : " . $e->getMessage()]);
}
