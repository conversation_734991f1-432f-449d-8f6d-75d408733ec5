<?php
header('Content-Type: application/json; charset=UTF-8');
require_once __DIR__ . '/../commen/db_connection.php';

// السماح فقط بطلبات POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(["error" => "Méthode non autorisée."]);
    exit;
}

// استقبال البيانات من JSON
$data = json_decode(file_get_contents("php://input"), true);

// التحقق من وجود القيم المطلوبة
$idUtilisateur = $data['idUtilisateur'] ?? null;
$newEmail = $data['email'] ?? null;

if (!$idUtilisateur || !$newEmail) {
    http_response_code(400);
    echo json_encode(["error" => "idUtilisateur et email sont requis."]);
    exit;
}

try {
    // التحقق من أن البريد الجديد غير مستخدم من قبل مستخدم آخر
    $checkSql = "SELECT COUNT(*) FROM Utilisateur WHERE email = :email AND idUtilisateur != :id";
    $checkStmt = $pdo->prepare($checkSql);
    $checkStmt->execute([
        'email' => $newEmail,
        'id' => $idUtilisateur
    ]);

    if ($checkStmt->fetchColumn() > 0) {
        http_response_code(409);
        echo json_encode(["error" => "Cet email est déjà utilisé par un autre utilisateur."]);
        exit;
    }

    // تنفيذ التحديث
    $updateSql = "UPDATE Utilisateur SET email = :email WHERE idUtilisateur = :id";
    $updateStmt = $pdo->prepare($updateSql);
    $updateStmt->execute([
        'email' => $newEmail,
        'id' => $idUtilisateur
    ]);

    echo json_encode(["message" => "Email mis à jour avec succès."]);

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(["error" => "Erreur base de données : " . $e->getMessage()]);
}
