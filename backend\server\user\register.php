<?php
header('Content-Type: application/json; charset=UTF-8');
require_once __DIR__ . '/../commen/db_connection.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(["error" => "Méthode non autorisée"]);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

$email = $input['email'] ?? null;
$password = $input['password'] ?? null;
$nom = $input['nom'] ?? null;
$prenom = $input['prenom'] ?? null;

$role = 'passager';
$etatCompte = 'activé';

if (!$email || !$password || !$nom || !$prenom) {
    http_response_code(400);
    echo json_encode(["error" => "Tous les champs sont requis"]);
    exit;
}

try {
    // التحقق من عدم وجود بريد إلكتروني مكرر
    $check = $pdo->prepare("SELECT COUNT(*) FROM Utilisateur WHERE email = :email");
    $check->execute(['email' => $email]);
    if ($check->fetchColumn() > 0) {
        http_response_code(409);
        echo json_encode(["error" => "Cet email est déjà utilisé"]);
        exit;
    }

    // تشفير كلمة المرور
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

    // إدراج في جدول Utilisateur
    $insertUser = $pdo->prepare("INSERT INTO Utilisateur (email, motDePasse, role, etatCompte) VALUES (:email, :motDePasse, :role, :etatCompte)");
    $insertUser->execute([
        ':email' => $email,
        ':motDePasse' => $hashedPassword,
        ':role' => $role,
        ':etatCompte' => $etatCompte
    ]);

    $idUtilisateur = $pdo->lastInsertId();

    // إدراج في جدول Passager
    $insertPassager = $pdo->prepare("INSERT INTO Passager (idUtilisateur, nom, prenom) VALUES (:idUtilisateur, :nom, :prenom)");
    $insertPassager->execute([
        ':idUtilisateur' => $idUtilisateur,
        ':nom' => $nom,
        ':prenom' => $prenom
    ]);

    // جلب المستخدم المُسجل للرد
    $stmt = $pdo->prepare("SELECT idUtilisateur, email, role, etatCompte FROM Utilisateur WHERE idUtilisateur = :id");
    $stmt->execute(['id' => $idUtilisateur]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    echo json_encode($user);

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(["error" => "Erreur base de données : " . $e->getMessage()]);
}
