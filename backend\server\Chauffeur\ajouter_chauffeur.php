<?php
header('Content-Type: application/json');
require_once __DIR__ . '/../commen/db_connection.php';

$response = [];

if ($_SERVER["REQUEST_METHOD"] === "POST") {
    // قراءة البيانات من JSON المرسَل من Postman
    $data = json_decode(file_get_contents("php://input"), true);

    $nom = $data['nom'] ?? '';
    $prenom = $data['prenom'] ?? '';
    $email = $data['email'] ?? '';
    $motDePasse = $data['motDePasse'] ?? '';
    $role = $data['role'] ?? 'Chauffeur';
    $etatCompte = $data['etatCompte'] ?? 'activé';
    $numeroPermis = $data['numeroPermis'] ?? '';

    // التحقق من صحة الدور
    if ($role !== 'Chauffeur') {
        echo json_encode(['error' => 'Le rôle doit être Chauffeur.']);
        exit;
    }

    try {
        // تشفير كلمة المرور
        $motDePasseHashe = password_hash($motDePasse, PASSWORD_BCRYPT);

        // 1. إدخال المستخدم إلى جدول Utilisateur
        $stmt1 = $pdo->prepare("INSERT INTO Utilisateur (email, motDePasse, role, etatCompte) VALUES (?, ?, ?, ?)");
        $stmt1->execute([$email, $motDePasseHashe, $role, $etatCompte]);
        $idUtilisateur = $pdo->lastInsertId();

        // 2. إدخال السائق في جدول Chauffeur
        $stmt2 = $pdo->prepare("INSERT INTO Chauffeur (nom, prenom, numeroPermis, idUtilisateur) VALUES (?, ?, ?, ?)");
        $stmt2->execute([$nom, $prenom, $numeroPermis, $idUtilisateur]);
        $idChauffeur = $pdo->lastInsertId();

        // 3. إنشاء الكائن للإرجاع
        $chauffeur = [
            "idChauffeur" => $idChauffeur,
            "nom" => $nom,
            "prenom" => $prenom,
            "numeroPermis" => $numeroPermis,
            "idUtilisateur" => $idUtilisateur
        ];

        $response = $chauffeur;
    } catch (PDOException $e) {
        $response = ["error" => "Erreur Base de données: " . $e->getMessage()];
    }
} else {
    $response = ["error" => "Méthode non autorisée"];
}

echo json_encode($response);
