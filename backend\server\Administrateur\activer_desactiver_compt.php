<?php
header('Content-Type: application/json; charset=UTF-8');
require_once __DIR__ . '/../commen/db_connection.php';

// السماح فقط بطلبات POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(["error" => "Méthode non autorisée."]);
    exit;
}

// استقبال البيانات بصيغة JSON
$data = json_decode(file_get_contents("php://input"), true);

// التحقق من وجود القيم المطلوبة
$idUtilisateur = $data['idUtilisateur'] ?? null;
$etatCompte = $data['etatCompte'] ?? null;

if (!$idUtilisateur || !in_array($etatCompte, ['activé', 'désactivé'])) {
    http_response_code(400);
    echo json_encode(["error" => "idUtilisateur et état valide (activé/désactivé) requis."]);
    exit;
}

try {
    // تحديث حالة الحساب
    $sql = "UPDATE Utilisateur SET etatCompte = :etatCompte WHERE idUtilisateur = :idUtilisateur";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([
        ':etatCompte' => $etatCompte,
        ':idUtilisateur' => $idUtilisateur
    ]);

    echo json_encode(["message" => "État du compte mis à jour avec succès."]);

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(["error" => "Erreur base de données : " . $e->getMessage()]);
}
