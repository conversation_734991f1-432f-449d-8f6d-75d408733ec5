<?php
// حذف منطقي لخدمة نقل (تغيير الحالة إلى désactivé)
header("Content-Type: application/json; charset=UTF-8");
require_once __DIR__ . '/../commen/db_connection.php';

if (!isset($_GET['idService'])) {
    echo json_encode(["error" => "Paramètre 'idService' manquant."]);
    exit;
}

$idService = (int)$_GET['idService'];

try {
    // إعداد استعلام التحديث لتغيير الحالة إلى désactivé
    $sql = "UPDATE ServiceDeTransport SET etat = 'désactivé' WHERE idService = :idService";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':idService', $idService, PDO::PARAM_INT);
    $stmt->execute();

    if ($stmt->rowCount() > 0) {
        echo json_encode(["message" => "Service désactivé avec succès."]);
    } else {
        echo json_encode(["message" => "Aucun service trouvé avec cet ID ou déjà désactivé."]);
    }
} catch (PDOException $e) {
    echo json_encode(["error" => "Erreur base de données : " . $e->getMessage()]);
}
