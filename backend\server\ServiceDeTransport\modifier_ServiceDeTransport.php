<?php
header('Content-Type: application/json; charset=UTF-8');
require_once __DIR__ . '/../commen/db_connection.php';

// قراءة البيانات
$data = json_decode(file_get_contents("php://input"), true);

// التحقق من توفر idService
$idService = $data['idService'] ?? null;
if (!$idService) {
    echo json_encode(["error" => "idService est requis"]);
    exit;
}

// القيم الممكن تعديلها
$heureDepart = $data['heureDepart'] ?? null;
$heureArrivee = $data['heureArrivee'] ?? null;
$prix = $data['prix'] ?? null;
$idTrajet = $data['idTrajet'] ?? null;
$idMoyen = $data['idMoyen'] ?? null;
$idChauffeur = $data['idChauffeur'] ?? null;

try {
    $sql = "UPDATE ServiceDeTransport 
            SET heureDepart = :heureDepart, 
                heureArrivee = :heureArrivee, 
                prix = :prix,
                idTrajet = :idTrajet,
                idMoyen = :idMoyen,
                idChauffeur = :idChauffeur
            WHERE idService = :idService";

    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':heureDepart', $heureDepart);
    $stmt->bindParam(':heureArrivee', $heureArrivee);
    $stmt->bindParam(':prix', $prix);
    $stmt->bindParam(':idTrajet', $idTrajet);
    $stmt->bindParam(':idMoyen', $idMoyen);
    $stmt->bindParam(':idChauffeur', $idChauffeur);
    $stmt->bindParam(':idService', $idService);

    $stmt->execute();

    echo json_encode([
        "message" => "Service modifié avec succès.",
        "idService" => $idService
    ]);

} catch (PDOException $e) {
    echo json_encode(["error" => "Erreur base de données : " . $e->getMessage()]);
}
